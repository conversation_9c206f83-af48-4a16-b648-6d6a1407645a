import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
from pathlib import Path
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class EvacuationEfficiencyAnalyzer:
    """疏散效率分析器 - 绘制箱线图+散点+山脊组合图"""

    def __init__(self, data_root):
        self.data_root = Path(data_root)
        self.output_dir = self.data_root / "analysis_results" / "behavioral" / "EvacuationEfficiencyAnalysis"
        self.output_dir.mkdir(parents=True, exist_ok=True)

        print(f"疏散效率分析文件路径为:{self.output_dir}")

        # 定义颜色方案
        self.smoke_colors = {
            1: ['#E3F2FD', '#2196F3', '#0D47A1'],  # 蓝色系 - 低浓度
            2: ['#FFF3E0', '#FF9800', '#E65100'],  # 橙色系 - 中浓度  
            3: ['#FFEBEE', '#F44336', '#B71C1C']   # 红色系 - 高浓度
        }
        
        # NPC数量对应的色值索引
        self.npc_color_idx = {0: 0, 10: 1, 20: 2}

    def analyze_evacuation_efficiency(self):
        """分析疏散效率并可视化"""
        print("###开始分析疏散效率###")

        # 读取行为数据文件
        behavioral_file = self.data_root / "BehavioralData.csv"
        if not behavioral_file.exists():
            print("未找到行为数据文件")
            return
        
        df = pd.read_csv(behavioral_file, encoding='utf-8-sig', engine='python')

        # 筛选第二次实验数据
        df_exp2 = df[df['experiment'] == 2].copy()

        if len(df_exp2) == 0:
            print("未找到第二次行为数据")
            return
        
        print(f"找到{len(df_exp2)}条第二次行为数据")

        # 创建分组标签
        df_exp2['group_label'] = df_exp2.apply(
            lambda row: f"烟气{int(row['smoke_level'])}_NPC{int(row['npc_count'])}", 
            axis=1
        )

        # 绘制三种指标的图表
        metrics = [
            ('evacuation_time', '疏散时间 (s)', 'evacuation_time_analysis'),
            ('evacuation_distance', '疏散距离 (m)', 'evacuation_distance_analysis'),
            ('evacuation_velocity', '疏散平均速度 (m/s)', 'evacuation_velocity_analysis')
        ]

        for metric, ylabel, filename in metrics:
            self.plot_combined_analysis(df_exp2, metric, ylabel, filename)
            
        print("###疏散效率分析完成###")

    def plot_combined_analysis(self, df, metric, ylabel, filename):
        """绘制箱线图+散点图+山脊图组合"""

        # 创建图形
        fig, ax = plt.subplot(figsize=(16, 9))

        # 获取所有组合
        smoke_levels = [1, 2, 3]
        npc_counts = [0, 10, 20]

        positions = []
        group_data = []
        group_labels = []
        colors = []

        pos = 0
        for smoke in smoke_levels:
            for npc in npc_counts:
                group_label = f"烟气{smoke}_NPC{npc}"

                data = df[df['smoke_level'] == smoke][df['npc_count'] == npc][metric]

                if len(data) > 0:
                    positions.append(pos)
                    group_data.append(data.values)
                    group_labels.append(group_label)

                    # 获取颜色
                    color_idx = self.npc_color_idx[npc]
                    color = self.smoke_colors[smoke][color_idx]
                    colors.append(color)

                pos += 1
        
        if not group_data:
            print(f"没有有效数据绘制{metric}")
            plt.close()
            return
        
        # 绘制箱线图
        box_width = 0.25
        bos_positions = [p - 0.3 for p in positions]

        bp = ax.boxplot(group_data, positions=bos_positions, widths=box_width, patch_artist=True, showfliers=False)
        # 设置箱线图颜色
        for patch, color in zip(bp['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)

        # 绘制散点图
        scatter_positions = positions
        for i, (pos, data, color) in enumerate(zip(scatter_positions, group_data, colors)):
            # 添加随机抖动
            jitter = np.random.normal(0, 0.05, len(data))
            ax.scatter([pos] * len(data) + jitter, data,
                       color=color, alpha=0.6, s=30, edgecolors='black', linewidth=0.5)
            
            # 绘制半小提琴图
            violin_positions = [p + 0.3 for p in positions]

            for i, (pos, data, color) in enumerate(zip(violin_positions, group_data, colors)):
                if len(data) > 1:
                    # 计算核密度估计
                    density = stats.gaussian_kde(data)

                    # 创建y轴范围
                    y_min, y_max = np.min(data), np.max(data)
                    y_range = np.linspace(y_min, y_max, 100)

                    # 计算密度
                    density_values = density(y_range)

                    # 归一化密度值
                    density_values = density_values / np.max(density_values, color=color, alpha=0.6)
                    ax.plot(pos + density_values, y_range, color='black', linewidth=1)

           # 设置图形属性
            ax.set_xticks(positions)
            ax.set_xticklabels([label.replace('_', '\n') for label in group_labels], 
                            rotation=45, ha='right')
            ax.set_ylabel(ylabel, fontsize=12)
            ax.set_title(f'疏散效率分析 - {ylabel}', fontsize=14, fontweight='bold')
            ax.grid(True, alpha=0.3)

            # 添加图例
            legend_elements = []
            for smoke in smoke_levels:
                for npc in npc_counts:
                    color_idx = self.npc_color_idx[npc]
                    color = self.smoke_colors[smoke][color_idx]
                    legend_elements.append(
                        plt.Rectangle((0,0),1,1, facecolor=color, alpha=0.7,
                                    label=f'烟气{smoke}_NPC{npc}')
                    )

            ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.15, 1), ncol=1)
        
            plt.tight_layout()
        
            # 保存图片
            for fmt in ['png', 'svg']:
                save_path = self.output_dir / f"{filename}.{fmt}"
                plt.savefig(save_path, dpi=600, bbox_inches='tight', format=fmt)
                
            plt.close()
            print(f"{ylabel}分析图已保存")

def get_grand_parent_folder():
    current_script_path = os.path.abspath(__file__)
    current_folder = os.path.dirname(current_script_path)
    parent_folder = os.path.dirname(current_folder)
    return parent_folder


def get_target_file_path():
    return get_grand_parent_folder() + os.sep + "ScriptsTest"

if __name__ == "main":
    # 创建分析器实例
    analyzer = EvacuationEfficiencyAnalyzer(get_target_file_path())

    # 执行分析
    analyzer.analyze_evacuation_efficiency()

        
        