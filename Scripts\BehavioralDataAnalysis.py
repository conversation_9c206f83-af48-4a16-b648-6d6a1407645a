import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import json
import warnings
from shapely.geometry import LineString
from pathlib import Path
from datetime import datetime
from matplotlib.patches import Rectangle

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class BehavioralDataAnalyzer:
    """行为数据分析器"""

    def __init__(self, data_root):
        self.data_root = Path(data_root)
        self.output_dir = self.data_root / "analysis_results" / "behavioral" # 使用“/”拼接路径,生成新的子目录
        self.output_dir.mkdir(parents=True, exist_ok=True) # 在文件夹中创建output_dir指向的目录
        # parents=True表示如果父目录不存在，自动补全；exist_ok=True表示如果目录已存在，不报错

        # 打印确保文件路径没问题
        print(f"行为数据分析文件路径为：{self.output_dir}")

        # 出口坐标定义（根据实际场景调整）
        self.left_exit = np.array([51.3, -2.424, -2.872])   # 左侧出口坐标
        self.right_exit = np.array([-228.7, -2.424, -2.769])   # 右侧出口坐标
        
        # 速度阈值设置
        self.velocity_threshold = 1.5  # 速度骤降阈值
        self.min_decision_duration = 0.5  # 最小犹豫时间（秒）
        self.hesitation_threshold = 1.0  # 犹豫点阈值（秒）
        
        self.behavioral_results = []

        # 定义决策区边界
        self.decision_zones = {
            'zone1':{
                'bound':[(-45.0, -40.0), (-1.345, 0.801)],
                'title':'决策区1'
            },
            'zone2':{
                'bound':[(-45.0, -40.0), (-3.238, -1.869)],
                'title':'决策区2'
            },
            'zone3':{
                'bound':[(-36.66, 6.51), (-3.328, -1.53)],
                'title':'决策区3'
            }
        }

    def analyze_all_data(self):
        """分析所有行为数据"""
        print("###开始分析行为数据###")

        # 读取总标签文件
        label_file = self.data_root / "all_file_labels.csv"
        if not label_file.exists():
            print("未找到总标签文件, 请先运行标签处理脚本")
            return
        
        labels_df = pd.read_csv(label_file, encoding='utf-8-sig', engine='python')

        # 根据data_type筛选Subject文件
        subject_files = labels_df[labels_df['data_type']==1].copy()

        print(f"找到{len(subject_files)}个Subject数据文件")

        # 分析每个文件
        for idx, row in subject_files.iterrows():
            self.analyze_single_data(row)

        # 保存分析结果
        self.save_behavioral_data()

        # 生成可视化图表
        self.generate_visualizations()

        print("###行为数据分析完成###")

    def analyze_single_data(self, file_info):
        """分析单个行为数据文件"""

        # 构建合并、清洗、整理后的文件路径
        file_path = Path(file_info['file_path'])
        organized_file_path = file_path.parent / f"organized_clean_merged_{file_path.stem}.csv"

        if not organized_file_path.exists():
            print(f"文件不存在: {organized_file_path}")
            return

        print(f"###开始分析单个行为数据文件:{organized_file_path.name}###")

        try:
            # 读取数据
            df = pd.read_csv(organized_file_path, encoding='utf-8-sig', engine='python')

            # 检查必要列
            required_cols = ['TimeStamp', 'PosX', 'PosY', 'PosZ', 'VeloX', 'VeloY', 'VeloZ', 'AbsVelo']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                print(f"缺少必要列: {missing_cols}")
                return
            
            # 处理时间戳 暂时不处理，处理完会丢失毫秒数据
            #df['TimeStamp'] = pd.to_datetime(df['TimeStamp'], format='%Y:%m:%H:%M:%S:%f', errors='coerce')
            #df = df.dropna(subset=['TimeStamp']).sort_values('TimeStamp').reset_index(drop=True)

            # 分析各项指标
            result = {
                'file_path': str(organized_file_path),
                'experiment': file_info['experiment'],
                'group': file_info['group'],
                'subject_id': file_info['subject_id'],
                'door_condition': file_info['door_condition'],
                'spawn_point': file_info['spawn_point'],
                'smoke_level': file_info['smoke_level'],
                'npc_count': file_info['npc_count'],
                'is_mirror': file_info['is_mirror']
            }

            # 1.疏散方向选择统计
            result['evacuation_direction'] = self.analyze_evacuation_direction(df)

            # 若场景为镜像的，那么选择左相当于原场景的右
            if  file_info['is_mirror'] == True:
                if result['evacuation_direction'] == 'left':
                    result['evacuation_direction'] = 'right'
                else:
                    result['evacuation_direction'] = 'left'

            # 2.疏散路径分析
            route_analysis = self.analyze_evacuation_route(df, file_info)
            result['evacuation_route'] = route_analysis['route_plot_path']
            result['is_return'] = route_analysis['has_return']

            # 3.疏散速度和距离
            result['evacuation_velocity'] = self.calculate_average_velocity(df)
            result['evacuation_distance'] = self.calculate_total_distance(df)

            # 4.疏散时间
            result['evacuation_time'] = self.calculate_evacuation_time(df)

            # 5.犹豫点识别
            hesitation_count, hesitation_positions = self.identify_decision_points(df)
            result['Hesitation_point'] = hesitation_count
            result['Hesitation_position'] = '; '.join(hesitation_positions) if hesitation_positions else ''

            # 将速度和距离转为数值
            result['evacuation_velocity'] = pd.to_numeric(result['evacuation_velocity'], errors='coerce').round(3)
            result['evacuation_distance'] = pd.to_numeric(result['evacuation_distance'], errors='coerce').round(3)

            self.behavioral_results.append(result)

        except Exception as e:
            print(f"分析单个行为数据文件时出错: {e}")
            return

    def analyze_evacuation_direction(self, df):
        """统计分析疏散方向选择"""

        # 获取最后位置
        final_pos = np.array([df.iloc[-1]['PosX'],df.iloc[-1]['PosY'], df.iloc[-1]['PosZ']])

        # 计算到两个出口的距离
        dist_left = np.linalg.norm(final_pos - self.left_exit)
        dist_right = np.linalg.norm(final_pos - self.right_exit)

        # 判断疏散方向
        if dist_left < dist_right:
            return 'left'
        else:
            return 'right'


    def analyze_evacuation_route(self, df, file_info):
        """分析疏散路径并绘制，统计折返情况"""
        # 提取位置数据
        postions = df[['PosX', 'PosY', 'PosZ']].values

        # 检测折返
        has_return = self.detect_path_return(postions)

        # 绘制疏散路径
        route_plot_path = self.plot_evacuation_route(df, file_info)

        return {
            'has_return': has_return,
            'route_plot_path': route_plot_path
        }

    def detect_path_return(self, postions):
        """通过路径自交检测，检测路径是否有折返"""
        if len(postions) < 4:
            return False
        
        try:
            # 使用X-Z平面创建路径线
            line = LineString(postions[:, [0,2]])

            # 检查自交
            return not line.is_simple
        except:
            return False
    
    def get_decision_zone4_bounds(self, smoke_level):
        """根据烟气等级获取决策区4的边界"""

        if smoke_level == 1:
            return [(17.99, 39.088), (-3, -1.3)]
        elif smoke_level == 2:
            return [(33.472, 39.088), (-3, -1.3)]
        else:
            return [(41.866, 47.62), (-1.3, 1.615)]
        
    def plot_evacuation_route(self, df, file_info):
        """绘制疏散路径图"""
        # 创建文件名
        filename = f"route_{file_info['subject_id']}_{file_info['experiment']}_{file_info['group']}_{file_info['door_condition']}_{file_info['spawn_point']}_{file_info['smoke_level']}.png"
        plot_path = self.output_dir / "routes" / filename
        plot_path.parent.mkdir(parents=True, exist_ok=True)

        # 获取决策区4的边界
        zone4_bound = self.get_decision_zone4_bounds(file_info['smoke_level'])
        decision_zones = self.decision_zones.copy()
        decision_zones['zone4'] = {
            'bound':zone4_bound,
            'title':'决策区4'
        }

        # 绘制2D轨迹图
        plt.figure(figsize=(12, 8))

        # 绘制路径
        plt.plot(df['PosX'], df['PosZ'], 'b-', linewidth=2, alpha=0.7, label='疏散路径')
        
        # 标记起点和终点
        plt.scatter(df.iloc[0]['PosX'], df.iloc[0]['PosZ'], 
                   c='green', s=100, marker='o', label='起点', zorder=5)
        plt.scatter(df.iloc[-1]['PosX'], df.iloc[-1]['PosZ'], 
                   c='red', s=100, marker='s', label='终点', zorder=5)
        
        # 标记出口
        plt.scatter(self.left_exit[0], self.left_exit[2], 
                   c='orange', s=150, marker='^', label='左出口', zorder=5)
        plt.scatter(self.right_exit[0], self.right_exit[2], 
                   c='purple', s=150, marker='^', label='右出口', zorder=5)
        
        plt.ylim(-15,15)
        plt.xlabel('X坐标 (m)')
        plt.ylabel('Z坐标 (m)')
        plt.title(f'疏散路径 - {file_info["subject_id"]} (烟雾等级: {file_info["smoke_level"]})')
        plt.legend()
        plt.grid(True, alpha=0.3)
        #plt.axis('equal')

        # 在图中添加决策区边界
        self.draw_decision_zones(plt, file_info['smoke_level'])
        
        plt.tight_layout()
        plt.savefig(plot_path, dpi=600, bbox_inches='tight')
        plt.close()
        
        return str(plot_path.relative_to(self.data_root))
    
    def draw_decision_zones(self, plt, smoke_level):
        """在疏散路径图中添加决策区边界"""
        # 绘制决策区1-3
        for zone_name, zone_info in self.decision_zones.items():
            x_bounds, z_bounds = zone_info['bound']
            rect = Rectangle((x_bounds[0], z_bounds[0]), x_bounds[1] - x_bounds[0], z_bounds[1] - z_bounds[0],
                             linewidth=2, edgecolor='royalblue', facecolor='none', linestyle='--', alpha=0.7)
            # gca:get current axis的缩写
            plt.gca().add_patch(rect)
        # 绘制决策区4
        zone4_bounds = self.get_decision_zone4_bounds(smoke_level)
        x_bounds, z_bounds = zone4_bounds
        rect = Rectangle((x_bounds[0], z_bounds[0]), x_bounds[1] - x_bounds[0], z_bounds[1] - z_bounds[0],
                         linewidth=2, edgecolor='royalblue', facecolor='none', linestyle='--', alpha=0.7)
        plt.gca().add_patch(rect)
    
    def calculate_average_velocity(self, df):
        """计算整个疏散过程的平均速度"""
        return df['AbsVelo'].mean()
    
    def calculate_total_distance(self, df):
        """计算疏散过程中的总路径长度"""
        positions = df[['PosX', 'PosY', 'PosZ']].values
        distances = np.sqrt(np.sum(np.diff(positions, axis=0)**2, axis=1)) # np.diff表示计算相邻两个位置的差值，axis=0表示沿着行方向进行计算
        return np.sum(distances)

    def calculate_evacuation_time(self, df):
        """计算疏散时间"""
        # 先对时间戳进行处理
        df['TimeStamp'] = pd.to_datetime(df['TimeStamp'], format='%Y:%m:%H:%M:%S:%f', errors='coerce')
        df = df.dropna(subset=['TimeStamp']).sort_values('TimeStamp').reset_index(drop=True)
        
        start_time = df.iloc[0]['TimeStamp']
        end_time = df.iloc[-1]['TimeStamp']
        return float((end_time - start_time).total_seconds())
    
    def identify_decision_points(self, df):
        """识别犹豫点(速度为零的持续时间超过阈值的点)"""
        velocities = df['AbsVelo'].values
        hesitation_points = []
        hesitation_positions = []

        # 寻找速度为零的连续时间段
        i = 0
        while i < len(velocities):
            if velocities[i] == 0:  # 发现速度为零的点
                start_index = i
                duration_count = 0

                # 计算连续速度为零的持续时间
                while i < len(velocities) and velocities[i] == 0:
                    duration_count += 1
                    i += 1

                # 计算实际持续时间（秒）
                # 假设数据采样频率为每秒若干次，这里需要根据时间戳计算
                if duration_count > 1:
                    start_time = df.iloc[start_index]['TimeStamp']
                    end_time = df.iloc[min(start_index + duration_count - 1, len(df) - 1)]['TimeStamp']

                    # 转换时间戳为datetime对象进行计算
                    if isinstance(start_time, str):
                        start_time = pd.to_datetime(start_time, format='%Y:%m:%H:%M:%S:%f', errors='coerce')
                    if isinstance(end_time, str):
                        end_time = pd.to_datetime(end_time, format='%Y:%m:%H:%M:%S:%f', errors='coerce')

                    if pd.notna(start_time) and pd.notna(end_time):
                        duration_seconds = (end_time - start_time).total_seconds()
                    else:
                        # 如果时间戳解析失败，使用帧数估算（假设30fps）
                        duration_seconds = duration_count / 30.0
                else:
                    duration_seconds = 0

                # 如果持续时间超过阈值，记录为犹豫点
                if duration_seconds >= self.hesitation_threshold:
                    # 取犹豫时间段的中点作为犹豫点位置
                    mid_index = start_index + duration_count // 2
                    hesitation_points.append({
                        'time_index': mid_index,
                        'start_index': start_index,
                        'duration_seconds': duration_seconds,
                        'duration_frames': duration_count,
                        'position': [df.iloc[mid_index]['PosX'], df.iloc[mid_index]['PosY'], df.iloc[mid_index]['PosZ']]
                    })

                    # 记录犹豫点位置（格式化为字符串）
                    pos_str = f"({df.iloc[mid_index]['PosX']:.2f}, {df.iloc[mid_index]['PosY']:.2f}, {df.iloc[mid_index]['PosZ']:.2f})"
                    hesitation_positions.append(pos_str)
            else:
                i += 1

        # 返回犹豫点数量和位置信息
        return len(hesitation_points), hesitation_positions
    
    def save_behavioral_data(self):
        """保存行为数据分析结果"""
        if not self.behavioral_results:
            print("没有分析结果可保存")
            return
        
        # 转换为DataFrame
        df = pd.DataFrame(self.behavioral_results)
        
        # 保存CSV文件
        output_file = self.data_root / "BehavioralData.csv"
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"行为数据分析结果已保存: {output_file}")
        
        # 生成统计摘要
        self.generate_summary_statistics(df)

    def generate_summary_statistics(self, df):
        """生成统计摘要"""
        summary = {
            'generation_time': datetime.now().strftime('%Y-%m %H:%M:%S'),
            'total_experiments': len(df),
            'direction_distribution': df['evacuation_direction'].value_counts().to_dict(),
            'return_rate': float(df['is_return'].sum() / len(df)),
            'average_metrics': {
                'evacuation_time': df['evacuation_time'].mean(),
                'evacuation_velocity': float(df['evacuation_velocity'].mean()),
                'evacuation_distance': float(df['evacuation_distance'].mean()),
                'hesitation_points': float(df['Hesitation_point'].mean())
            },
            'by_smoke_level': {},
            'by_npc_count': {}
        }

        # 按烟雾等级统计
        for smoke_level in df['smoke_level'].unique(): # unique()可以去除对象中的重复元素并返回一个新的列表
            if pd.notna(smoke_level): # notna()检测非缺失值，返回True表示对应位置有有效数据
                subset = df[df['smoke_level'] == smoke_level]
                summary['by_smoke_level'][int(smoke_level)] = {
                    'count': float(len(subset)),
                    'avg_time': subset['evacuation_time'].mean(),
                    'avg_velocity': float(subset['evacuation_velocity'].mean()),
                    'return_rate': float(subset['is_return'].sum() / len(subset))
                }

        # 按npc数量统计
        for npc_count in df['npc_count'].unique():
            if pd.notna(npc_count):
                subset = df[df['npc_count'] == npc_count]
                summary['by_npc_count'][int(npc_count)] = {
                   'count': float(len(subset)),
                    'avg_time': subset['evacuation_time'].mean(),
                    'avg_velocity': float(subset['evacuation_velocity'].mean()),
                    'return_rate': float(subset['is_return'].sum() / len(subset))
                }

        # 保存统计摘要
        stats_file = self.data_root / "behavioral_statistics.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
       
        print(f"统计摘要已保存: {stats_file}")

    def generate_visualizations(self):
        """生成可视化图表"""
        if not self.behavioral_results:
            return
        
        df = pd.DataFrame(self.behavioral_results)
        
        # 1. 疏散方向选择统计
        self.plot_direction_distribution(df)
        
        # 2. 不同烟雾浓度的路径对比
        self.plot_routes_by_smoke_level(df)
        
        # 3. 疏散速度和距离箱线图
        self.plot_velocity_distance_boxplots(df)
        
        # 4. 疏散时间箱线图
        self.plot_evacuation_time_boxplots(df)
        
        print("可视化图表生成完成")

    def plot_direction_distribution(self, df):
        """绘制疏散方向分布图"""
        plt.figure(figsize=(12, 8)) # 控制图像的物理尺寸，这里为宽12英寸，高8英寸

        # 按组别和烟雾等级分组
        grouped = df.groupby(['group', 'smoke_level'])['evacuation_direction'].value_counts().unstack(fill_value=0)
        # 首先根据组别和烟雾等级将数据分为多个子集，其次通过value_counts()对每个子集内的疏散方向进行计数
        # 最后通过unstack()将结果展开为一个二维表格，即将'evacuation_direction'转换为列名，缺失值用0填充

        # 绘制分组条形图
        ax = grouped.plot(kind='bar', figsize=(12, 8), width=0.8)
        plt.title('疏散方向选择分布', fontsize=16)
        plt.xlabel('组别-烟雾等级', fontsize=12)
        plt.ylabel('人数', fontsize=12)
        plt.legend(title='疏散方向', labels=['左侧安全出口', '右侧安全出口'])
        plt.xticks(rotation=45)
        plt.tight_layout()

        # 保存图片
        plt.savefig(self.output_dir / 'direction_distribution.png', dpi=600, bbox_inches='tight')
        plt.close()

        print("疏散方向分布图已保存")

    def plot_routes_by_smoke_level(self, df):
        """绘制不同烟雾浓度和NPC数量组合的路径对比图（逐渐叠加）"""
        
        # 1. 仅处理第二部分实验数据
        df_part2 = df[df['experiment'] == 2].copy()
        
        if len(df_part2) == 0:
            print("没有找到第二部分实验数据")
            return
        
        # 镜像参考点
        mirror_point = (-92.5, -0.4318, -0.4991)
        
        # 创建3x3子图布局
        fig, axes = plt.subplots(3, 3, figsize=(18, 15))
        fig.suptitle('第二部分实验：不同烟雾浓度和NPC数量下的疏散路径对比（逐渐叠加）', fontsize=16)
        
        smoke_levels = [1, 2, 3]  # 低、中、高
        npc_counts = [0, 10, 20]  # 无、10个、20个NPC
        colors = ['yellow', 'orange', 'red']  # 对应NPC 0, 10, 20的颜色
        
        # 存储每个烟雾等级的累积轨迹数据
        accumulated_trajectories = {smoke_level: [] for smoke_level in smoke_levels}
        
        for i, smoke_level in enumerate(smoke_levels):
            for j, npc_count in enumerate(npc_counts):
                ax = axes[i, j]
                
                # 筛选当前组合的数据
                subset = df_part2[(df_part2['smoke_level'] == smoke_level) & 
                                 (df_part2['npc_count'] == npc_count)]
                
                # 先绘制之前累积的轨迹（较浅的颜色）
                for prev_j, prev_trajectories in enumerate(accumulated_trajectories[smoke_level]):
                    if prev_j < j:  # 只绘制之前的NPC条件
                        for traj_data in prev_trajectories:
                            ax.plot(traj_data['pos_x'], traj_data['pos_z'], 
                                   color=colors[prev_j], alpha=0.5, linewidth=0.8)
                
                # 绘制当前NPC条件的轨迹
                current_trajectories = []
                if len(subset) > 0:
                    for _, row in subset.iterrows():
                        try:
                            file_path = Path(row['file_path'])
                            data = pd.read_csv(file_path, encoding='utf-8-sig')
                            
                            # 检查是否为镜像条件，进行坐标变换
                            if 'mirror' in str(file_path).lower() or row.get('is_mirror', False):
                                pos_x = 2 * mirror_point[0] - data['PosX']
                                pos_z = data['PosZ']
                            else:
                                pos_x = data['PosX']
                                pos_z = data['PosZ']
                            
                            # 存储轨迹数据用于后续叠加
                            current_trajectories.append({
                                'pos_x': pos_x,
                                'pos_z': pos_z
                            })
                            
                            # 绘制当前轨迹（较深的颜色）
                            ax.plot(pos_x, pos_z, color=colors[j], alpha=1, linewidth=1)
                            
                        except Exception as e:
                            print(f"绘制路径时发生错误: {e}")
                            continue
                    
                    # 将当前轨迹添加到累积数据中
                    if len(accumulated_trajectories[smoke_level]) <= j:
                        accumulated_trajectories[smoke_level].append(current_trajectories)
                    else:
                        accumulated_trajectories[smoke_level][j] = current_trajectories
                
                # 如果没有数据，显示提示
                if len(subset) == 0:
                    ax.text(0.5, 0.5, '无数据', ha='center', va='center', transform=ax.transAxes)
                
                # 标记出口
                ax.scatter(self.left_exit[0], self.left_exit[2], 
                          c='green', s=100, marker='^', label='左出口', zorder=5)
                ax.scatter(self.right_exit[0], self.right_exit[2], 
                          c='red', s=100, marker='^', label='右出口', zorder=5)
                
                ax.set_ylim(-5, 5)
                ax.set_xlabel('X坐标 (m)')
                ax.set_ylabel('Z坐标 (m)')
                ax.set_title(f'烟雾{smoke_level} + NPC{npc_count}')
                ax.grid(True, alpha=0.3)
                
                # 添加颜色图例（只在第一行显示）
                if i == 0:
                    legend_elements = []
                    for k in range(j + 1):
                        legend_elements.append(plt.Line2D([0], [0], color=colors[k], 
                                                        label=f'NPC{npc_counts[k]}'))
                    ax.legend(handles=legend_elements, loc='upper right')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'routes_by_smoke_level.png', dpi=600, bbox_inches='tight')
        plt.close()
        
        print("不同烟雾浓度和NPC数量下的疏散路径对比图已保存")

    def plot_velocity_distance_boxplots(self, df):
        """绘制速度和距离箱线图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # 速度箱线图
        df.boxplot(column='evacuation_velocity', by=['group', 'smoke_level'], ax=ax1)
        ax1.set_title('疏散平均速度分布')
        ax1.set_xlabel('组别-烟雾等级')
        ax1.set_ylabel('平均速度 (m/s)')

        # 距离箱线图
        df.boxplot(column='evacuation_distance', by=['group', 'smoke_level'], ax=ax2)
        ax2.set_title('疏散路径长度分布')
        ax2.set_xlabel('组别-烟雾等级')
        ax2.set_ylabel('路径长度 (m)')

        plt.tight_layout()
        plt.savefig(self.output_dir / 'velocity_distance_boxplots.png', dpi=600, bbox_inches='tight')
        plt.close()
       
        print("疏散速度和距离箱线图已保存")

    def plot_evacuation_time_boxplots(self, df):
        """绘制疏散时间箱线图"""
        plt.figure(figsize=(12, 8))

        # 按组别和烟雾等级分组
        df.boxplot(column='evacuation_time', by=['group', 'smoke_level'])
        plt.title('疏散时间分布', fontsize=16)
        plt.xlabel('组别-烟雾等级', fontsize=12)
        plt.ylabel('疏散时间 (秒)', fontsize=12)
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        plt.savefig(self.output_dir / 'evacuation_time_boxplots.png', dpi=600, bbox_inches='tight')
        plt.close()

        print("疏散时间箱线图已保存")


def get_grand_parent_folder():
    current_script_path = os.path.abspath(__file__)
    current_folder = os.path.dirname(current_script_path)
    parent_folder = os.path.dirname(current_folder)
    return parent_folder


def get_target_file_path():
    return get_grand_parent_folder() + os.sep + "ScriptsTest"


# 使用示例
if __name__ == "__main__":
    # 创建实例
    analyzer = BehavioralDataAnalyzer("D:\\File_VRProject\\ExperimentTunnelData\\ScriptsTest")

    # 更稳定
    # analyzer = BehavioralDataAnalyzer(get_target_file_path())

    # 分析所有数据
    analyzer.analyze_all_data()
