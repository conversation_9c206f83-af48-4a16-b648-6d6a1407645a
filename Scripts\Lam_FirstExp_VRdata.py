import os
import pandas as pd
import math
from datetime import datetime
from datetime import timedelta


def calculate_speed(df):
    """计算速度合量和平均速度"""
    # 计算每个点的速度合量
    df['Speed'] = df.apply(lambda row: math.sqrt(row['VeloX'] ** 2 + row['VeloZ'] ** 2), axis=1)
    # 计算平均速度
    avg_speed = df['Speed'].mean()
    return avg_speed


def parse_timestamp(timestamp_str):
    """解析自定义格式的时间戳字符串为datetime对象
    时间戳格式为: 年:月:时:分:秒:毫秒 (例如2025:06:15:50:30:136)
    """
    try:
        # 分割时间戳各部分
        parts = list(map(int, timestamp_str.split(':')))
        if len(parts) != 6:
            print(f"时间戳格式错误，应有6个部分，实际有{len(parts)}个: {timestamp_str}")
            return None

        year, month, hour, minute, second, millisecond = parts

        # 为了创建datetime对象，需要提供日，这里假设为1日
        day = 1

        # 创建基础datetime对象
        dt = datetime(year, month, day, hour, minute, second)

        # 添加毫秒
        dt += timedelta(milliseconds=millisecond)
        return dt
    except Exception as e:
        print(f"解析时间戳 {timestamp_str} 时出错: {str(e)}")
        return None


def calculate_evacuation_time(df, time_column_index=0):
    """计算疏散时间（最后一个时间戳与第一个时间戳的差值）"""
    # 获取时间列（第一列）的列名
    time_column = df.columns[time_column_index]

    # 获取第一个和最后一个时间戳（数据行从第二行开始）
    first_time_str = df.iloc[0][time_column]  # 第二行第一列
    last_time_str = df.iloc[-1][time_column]  # 最后一行第一列

    # 解析时间戳
    first_time = parse_timestamp(str(first_time_str))
    last_time = parse_timestamp(str(last_time_str))

    if not first_time or not last_time:
        return 0  # 解析失败

    # 计算时间差（秒）
    time_diff = (last_time - first_time).total_seconds()
    return time_diff


def calculate_path_length(df):
    """计算路径长度（基于连续坐标点的连线长度）"""
    path_length = 0.0

    # 遍历所有连续的点对
    for i in range(1, len(df)):
        x1, z1 = df['PosX'].iloc[i - 1], df['PosZ'].iloc[i - 1]
        x2, z2 = df['PosX'].iloc[i], df['PosZ'].iloc[i]

        # 计算两点之间的距离
        distance = math.sqrt((x2 - x1) ** 2 + (z2 - z1) ** 2)
        path_length += distance

    return path_length


def process_csv_file(file_path):
    """处理单个CSV文件，返回计算结果"""
    try:
        # 读取CSV文件
        df = pd.read_csv(file_path)

        # 检查必要的列是否存在
        required_columns = ['VeloX', 'VeloZ', 'PosX', 'PosZ']
        if not set(required_columns).issubset(df.columns):
            missing = [col for col in required_columns if col not in df.columns]
            print(f"警告: 文件 {file_path} 缺少必要的列: {missing}")
            return None

        # 检查数据行数是否足够
        if len(df) < 2:
            print(f"警告: 文件 {file_path} 数据行数不足")
            return None

        # 计算各项指标
        avg_speed = calculate_speed(df)
        evac_time = calculate_evacuation_time(df)
        path_length = calculate_path_length(df)

        # 获取文件名（不包含路径和扩展名）
        file_name = os.path.splitext(os.path.basename(file_path))[0]

        return {
            'file_name': file_name,
            'avg_speed': avg_speed,
            'evacuation_time_seconds': evac_time,
            'path_length': path_length
        }

    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return None


def process_directory(root_dir):
    """处理目录下的所有子文件夹和CSV文件"""
    # 存储所有文件的结果
    all_results = []
    # 存储每个文件夹的汇总结果
    folder_summaries = []

    # 遍历所有子文件夹
    for folder_name in os.listdir(root_dir):
        folder_path = os.path.join(root_dir, folder_name)

        # 只处理目录
        if not os.path.isdir(folder_path):
            continue

        print(f"正在处理文件夹: {folder_name}")

        # 存储当前文件夹中所有文件的结果
        folder_results = []

        # 遍历文件夹中的所有CSV文件
        for file_name in os.listdir(folder_path):
            if file_name.endswith('.csv'):
                file_path = os.path.join(folder_path, file_name)
                result = process_csv_file(file_path)

                if result:
                    # 添加文件夹信息
                    result['folder_name'] = folder_name
                    all_results.append(result)
                    folder_results.append(result)

        # 计算文件夹的平均值
        if folder_results:
            avg_speed = sum(r['avg_speed'] for r in folder_results) / len(folder_results)
            avg_evac_time = sum(r['evacuation_time_seconds'] for r in folder_results) / len(folder_results)
            avg_path_length = sum(r['path_length'] for r in folder_results) / len(folder_results)

            folder_summaries.append({
                'folder_name': folder_name,
                'file_count': len(folder_results),
                'avg_speed': avg_speed,
                'avg_evacuation_time_seconds': avg_evac_time,
                'avg_path_length': avg_path_length
            })

    return all_results, folder_summaries


def export_results(all_results, folder_summaries):
    """导出结果到指定路径的CSV文件"""
    # 定义输出目录和文件名
    output_dir = r"C:\Users\<USER>\Documents\地铁隧道疏散\实验\分析"
    base_filename = "VR实验数据"

    # 创建输出目录（如果不存在）
    if not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
        print(f"已创建输出目录: {output_dir}")

    # 获取当前时间作为文件名的一部分，避免覆盖
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 导出文件级结果
    file_output_path = os.path.join(output_dir, f'{base_filename}_文件级_{timestamp}.csv')
    file_results_df = pd.DataFrame(all_results)
    file_results_df.to_csv(file_output_path, index=False)
    print(f"文件级结果已导出到: {file_output_path}")

    # 导出文件夹级结果
    folder_output_path = os.path.join(output_dir, f'{base_filename}_文件夹级_{timestamp}.csv')
    folder_results_df = pd.DataFrame(folder_summaries)
    folder_results_df.to_csv(folder_output_path, index=False)
    print(f"文件夹级结果已导出到: {folder_output_path}")

    return file_output_path, folder_output_path


if __name__ == "__main__":
    # 目标根目录
    root_directory = r"D:\LamWork\ExperimentTunnelData\ExperimentTunnelData\FirstExpPosVeloData\GroupHaveNPC"

    # 检查目录是否存在
    if not os.path.exists(root_directory):
        print(f"错误: 目录 {root_directory} 不存在")
    else:
        # 处理数据
        file_results, folder_results = process_directory(root_directory)

        # 导出结果
        if file_results or folder_results:
            export_results(file_results, folder_results)
        else:
            print("没有找到可处理的CSV文件")
